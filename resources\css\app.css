@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for UniLink */
@layer components {
    /* Feed layout responsive adjustments */
    .feed-container {
        @apply max-w-2xl mx-auto;
    }

    /* Smooth transitions for interactive elements */
    .transition-colors {
        transition-property: color, background-color, border-color;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        transition-duration: 150ms;
    }

    /* Custom scrollbar for sidebars */
    .custom-scrollbar::-webkit-scrollbar {
        width: 6px;
    }

    .custom-scrollbar::-webkit-scrollbar-track {
        background: #f1f5f9;
    }

    .custom-scrollbar::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 3px;
    }

    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
        background: #94a3b8;
    }

    /* Line clamp utilities */
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .line-clamp-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
}

/* 3-Column Layout Styles */
@layer utilities {
    /* Ensure proper 3-column layout behavior */
    .three-column-layout {
        display: flex;
        height: 100vh;
        padding-top: 4rem; /* Account for fixed header */
    }

    .three-column-layout > * {
        flex-shrink: 0;
    }

    .three-column-layout .central-feed {
        flex: 1;
        min-width: 0; /* Prevents flex item from overflowing */
    }

    /* Ensure body doesn't scroll when using fixed layout */
    body.fixed-layout {
        overflow: hidden;
    }

    /* Force right sidebar to show on lg screens and up */
    @media (min-width: 1024px) {
        .force-show-right-sidebar {
            display: flex !important;
        }
    }

    /* Mobile responsive adjustments */
    @media (max-width: 1279px) {
        .xl\:mr-80 {
            margin-right: 0 !important;
        }

        .xl\:mr-96 {
            margin-right: 0 !important;
        }
    }

    /* Ensure sidebars don't interfere on smaller screens */
    @media (max-width: 1023px) {
        .lg\:ml-64 {
            margin-left: 0 !important;
        }
    }
}
