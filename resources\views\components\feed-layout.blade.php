<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'UniLink') }}</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])
    </head>
    <body class="font-sans antialiased bg-gray-100 overflow-hidden">
        <!-- Header Navigation -->
        @include('layouts.unilink-header')

        <!-- Main 3-Column Container -->
        <div class="flex h-screen pt-16">
            <!-- Left Sidebar -->
            <div class="hidden lg:flex lg:flex-col lg:w-64 xl:w-72 bg-white border-r border-gray-200 overflow-y-auto flex-shrink-0">
                @include('layouts.unilink-sidebar-content')
            </div>

            <!-- Central Feed -->
            <main class="flex-1 bg-gray-100 overflow-y-auto min-w-0">
                <div class="max-w-2xl mx-auto px-4 py-6">
                    <!-- Feed Header (if provided) -->
                    @isset($header)
                        <div class="mb-6">
                            {{ $header }}
                        </div>
                    @endisset

                    <!-- Main Feed Content -->
                    <div class="space-y-4">
                        {{ $slot }}
                    </div>
                </div>
            </main>

            <!-- Right Sidebar -->
            <div class="hidden xl:flex xl:flex-col xl:w-80 2xl:w-96 bg-white border-l border-gray-200 overflow-y-auto flex-shrink-0 force-show-right-sidebar">
                @include('layouts.unilink-right-sidebar-content')
            </div>
        </div>

        <!-- Mobile Sidebar -->
        <div class="lg:hidden">
            @include('layouts.unilink-sidebar')
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div x-data="{ open: false }"
             x-on:toggle-sidebar.window="open = !open"
             x-show="open"
             x-transition:enter="transition-opacity ease-linear duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition-opacity ease-linear duration-300"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 bg-gray-600 bg-opacity-75 z-30 xl:hidden"
             @click="$dispatch('toggle-sidebar')">
        </div>

        <!-- Notification Popup -->
        @include('components.notification-popup')
    </body>
</html>
